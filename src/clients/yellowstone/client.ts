import type { Metadata } from '@grpc/grpc-js'
import { type CreateGrpcClientOptions, call, createGrpcClient } from '../../utils'
import { type GetBlockHeightRequest, type GetBlockHeightResponse, type GetLatestBlockhashRequest, type GetLatestBlockhashResponse, type GetSlotRequest, type GetSlotResponse, type GetVersionRequest, type GetVersionResponse, GeyserClient, type IsBlockhashValidRequest, type IsBlockhashValidResponse, type PingRequest, type PongResponse, type SubscribeReplayInfoRequest, type SubscribeReplayInfoResponse } from './generated/geyser'
import { YellowstoneGeyserStreamWrapper, type YellowstoneGeyserStreamWrapperOptions } from './utils'

export type YellowstoneGeyserClientOptions = CreateGrpcClientOptions

export class YellowstoneGeyserClient {
    public readonly grpc: GeyserClient

    public constructor(url: string, protected readonly options: YellowstoneGeyserClientOptions = {}) {
        this.grpc = createGrpcClient(GeyserClient, url, { tokenMetadataKey: 'x-token', ...options })
    }

    public createStream(options?: YellowstoneGeyserStreamWrapperOptions) {
        return new YellowstoneGeyserStreamWrapper(this.grpc, options)
    }

    public async subscribeReplayInfo(request: SubscribeReplayInfoRequest = {}, metadata?: Metadata) {
        return call<SubscribeReplayInfoRequest, SubscribeReplayInfoResponse>(this.grpc.subscribeReplayInfo.bind(this.grpc), request, metadata)
    }

    public async ping(request: PingRequest, metadata?: Metadata) {
        return call<PingRequest, PongResponse>(this.grpc.ping.bind(this.grpc), request, metadata)
    }

    public async getLatestBlockhash(request: GetLatestBlockhashRequest = {}, metadata?: Metadata) {
        return call<GetLatestBlockhashRequest, GetLatestBlockhashResponse>(this.grpc.getLatestBlockhash.bind(this.grpc), request, metadata)
    }

    public async getBlockHeight(request: GetBlockHeightRequest = {}, metadata?: Metadata) {
        return call<GetBlockHeightRequest, GetBlockHeightResponse>(this.grpc.getBlockHeight.bind(this.grpc), request, metadata)
    }

    public async getSlot(request: GetSlotRequest = {}, metadata?: Metadata) {
        return call<GetSlotRequest, GetSlotResponse>(this.grpc.getSlot.bind(this.grpc), request, metadata)
    }

    public async isBlockhashValid(request: IsBlockhashValidRequest, metadata?: Metadata) {
        return call<IsBlockhashValidRequest, IsBlockhashValidResponse>(this.grpc.isBlockhashValid.bind(this.grpc), request, metadata)
    }

    public async getVersion(request: GetVersionRequest = {}, metadata?: Metadata) {
        return call<GetVersionRequest, GetVersionResponse>(this.grpc.getVersion.bind(this.grpc), request, metadata)
    }
}
