import type { ClientDuplexStream } from '@grpc/grpc-js'
import { resolveNestedOptions } from '@kdt310722/utils/object'
import { type Awaitable, createDeferredWithTimeout, pTap } from '@kdt310722/utils/promise'
import { StreamError } from '../errors'
import { StreamWriteError } from '../errors/stream-write-error'
import { HeartbeatManager, type HeartbeatManagerOptions } from './heartbeat-manager'
import { StreamWrapper, type StreamWrapperOptions, type StreamWrapperTimeoutOptions } from './stream-wrapper'

export interface DuplexStreamWrapperTimeoutOptions extends StreamWrapperTimeoutOptions {
    drain?: number
    write?: number
}

export interface DuplexStreamWrapperOptions<TRequest> extends Omit<StreamWrapperOptions, 'timeout'> {
    timeout?: DuplexStreamWrapperTimeoutOptions
    heartbeat?: HeartbeatManagerOptions<TRequest> | boolean
}

export class DuplexStreamWrapper<TRequest, TResponse, TStream extends ClientDuplexStream<TRequest, TResponse> = ClientDuplexStream<TRequest, TResponse>> extends StreamWrapper<TResponse, TStream> {
    protected readonly drainTimeout: number
    protected readonly writeTimeout: number
    protected readonly heartbeatManager: HeartbeatManager<TRequest>

    protected requestId = 0

    public constructor(subscriber: (signal: AbortSignal) => Awaitable<TStream>, { timeout = {}, heartbeat = true, ...options }: DuplexStreamWrapperOptions<TRequest> = {}) {
        super(subscriber, { ...options, timeout })

        this.drainTimeout = timeout.drain ?? 10_000
        this.writeTimeout = timeout.write ?? 10_000
        this.heartbeatManager = new HeartbeatManager<TRequest>(this.write.bind(this), (error) => this.stream?.destroy(error), resolveNestedOptions(heartbeat) || { enabled: false })
        this.addSubscribeEventHandler('finish', (promise) => promise.reject(new StreamError('Stream finished before subscription completed')))
    }

    public override async subscribe(signal?: AbortSignal) {
        return super.subscribe(signal).then(pTap(() => this.heartbeatManager.start()))
    }

    public write(data: TRequest, signal?: AbortSignal) {
        if (!this.stream) {
            throw new StreamError('Stream is not subscribed')
        }

        const id = ++this.requestId

        this.emit('write', data, id)

        this.processWrite(this.stream, id, data, AbortSignal.any([this.abortController.signal, ...(signal ? [signal] : [])])).catch((error) => {
            this.emit('error', error)
        })
    }

    protected async processWrite(stream: TStream, requestId: number, data: TRequest, signal?: AbortSignal) {
        if (signal?.aborted) {
            return
        }

        const isWritable = await this.waitForWritable(stream, signal)

        if (!isWritable) {
            throw new StreamWriteError('Stream is not writable').withData(data)
        }

        const wrote = createDeferredWithTimeout<void>(this.writeTimeout, () => new StreamWriteError('Write timeout').withData(data))
        const resolve = () => wrote.resolve()

        signal?.addEventListener('abort', resolve, { once: true })
        stream.once('error', resolve)

        this.once('close', resolve)

        try {
            const result = stream.write(data, (error?: Error) => {
                if (error) {
                    wrote.reject(new StreamWriteError('Stream write failed', { cause: error }).withData(data))
                } else {
                    wrote.resolve()
                    this.emit('wrote', data, requestId)
                }
            })

            if (!result) {
                wrote.reject(new StreamWriteError('Stream is not writable').withData(data))
            }
        } catch (error) {
            wrote.reject(new StreamWriteError('Stream write failed', { cause: error }).withData(data))
        }

        return wrote.finally(() => {
            signal?.removeEventListener('abort', resolve)
            stream.removeListener('error', resolve)
            this.off('close', resolve)
        })
    }

    protected async waitForWritable(stream: TStream, signal?: AbortSignal) {
        if (signal?.aborted) {
            return false
        }

        if (this.isWritable(stream)) {
            return true
        }

        const writable = createDeferredWithTimeout<boolean>(this.drainTimeout, () => new StreamError('Drain timeout'))

        const handleAbort = () => writable.resolve(false)
        const handleDrain = () => writable.resolve(true)
        const handleError = () => writable.resolve(false)

        signal?.addEventListener('abort', handleAbort, { once: true })
        stream.once('drain', handleDrain)
        stream.once('error', handleError)

        this.once('close', handleError)

        return writable.finally(() => {
            this.off('close', handleError)

            stream.removeListener('drain', handleDrain)
            stream.removeListener('error', handleError)
            signal?.removeEventListener('abort', handleAbort)
        })
    }

    protected isWritable(stream: TStream) {
        return !stream.writableEnded && stream.writable
    }

    protected override handleData(stream: TStream, data: TResponse) {
        super.handleData(stream, data)
        this.heartbeatManager.reset()
    }

    protected override reset() {
        this.heartbeatManager.stop()
        super.reset()
    }
}
