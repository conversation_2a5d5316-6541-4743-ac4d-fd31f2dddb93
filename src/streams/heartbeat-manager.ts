import { notNullish } from '@kdt310722/utils/common'
import { isFunction } from '@kdt310722/utils/function'
import { HeartbeatError } from '../errors'

export interface HeartbeatManagerOptions<TRequest> {
    enabled?: boolean
    interval?: number
    timeout?: number
    message?: TRequest | (() => TRequest)
}

export class HeartbeatManager<TRequest> {
    protected readonly isEnabled: boolean
    protected readonly interval: number
    protected readonly timeout: number
    protected readonly message?: TRequest | (() => TRequest)

    protected pingTimer?: NodeJS.Timeout
    protected pongTimer?: NodeJS.Timeout

    protected isStopped = false
    protected isRunning = false

    public constructor(protected readonly write: (request: TRequest) => void, protected readonly destroy: (error?: Error) => void, { enabled = true, interval = 30_000, timeout = 10_000, message }: HeartbeatManagerOptions<TRequest>) {
        this.isEnabled = enabled && notNullish(message) && interval > 0 && timeout > 0
        this.interval = interval
        this.timeout = timeout
        this.message = message
    }

    public start() {
        if (this.isEnabled) {
            this.isStopped = false
            this.pingTimer = setInterval(() => this.send(), this.interval)
        }
    }

    public stop() {
        this.isStopped = true
        this.reset()

        if (this.pingTimer) {
            clearInterval(this.pingTimer)
            this.pingTimer = undefined
        }
    }

    public reset() {
        if (this.pongTimer) {
            clearTimeout(this.pongTimer)
            this.pongTimer = undefined
        }
    }

    protected send() {
        if (!this.isEnabled || this.isRunning || this.isStopped || !this.message) {
            return
        }

        this.isRunning = true

        try {
            const message = isFunction(this.message) ? this.message() : this.message

            this.write(message)
            this.pongTimer = setTimeout(() => this.destroy(this.createError('Heartbeat response timed out')), this.timeout)
        } catch (error) {
            this.destroy(error instanceof Error ? error : this.createError('Failed to send heartbeat message', { cause: error }))
        } finally {
            this.isRunning = false
        }
    }

    protected createError(message: string, options?: ErrorOptions) {
        return new HeartbeatError(message, options).withTimeout(this.timeout).withInterval(this.interval)
    }
}
