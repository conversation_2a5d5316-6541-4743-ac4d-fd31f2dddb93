import type { ClientReadableStream } from '@grpc/grpc-js'
import { createAbortError } from '@kdt310722/utils/error'
import type { Fn } from '@kdt310722/utils/function'
import { type Awaitable, type DeferredPromise, createDeferredWithTimeout } from '@kdt310722/utils/promise'
import { StreamError } from '../errors'

export type CreateStreamEventHandlers<TStream> = Record<string, (promise: DeferredPromise<TStream>, ...args: any[]) => void>

export interface CreateStreamOptions<TStream> {
    timeout?: number
    signal?: AbortSignal
    eventHandlers?: CreateStreamEventHandlers<TStream>
}

export async function subscribeStream<TData, TStream extends ClientReadableStream<TData> = ClientReadableStream<TData>>(createStream: (signal: AbortSignal) => Awaitable<TStream>, { timeout = 30_000, signal, eventHandlers = {} }: CreateStreamOptions<TStream> = {}): Promise<TStream> {
    if (signal?.aborted) {
        throw signal?.reason ?? createAbortError('Subscription aborted before it started')
    }

    const abortController = new AbortController()
    const abortSignal = AbortSignal.any([abortController.signal, ...(signal ? [signal] : [])])

    const promise = createDeferredWithTimeout<TStream>(timeout, () => new StreamError('Subscribe timeout'), {
        onSettle: () => abortSignal.aborted || abortController.abort(),
    })

    let cleanup: Fn | undefined
    let onAbort: Fn | undefined

    const handleAbort = () => {
        cleanup?.()
        onAbort?.()
        promise.reject(abortSignal.reason ?? createAbortError('Subscription aborted'))
    }

    abortSignal.addEventListener('abort', handleAbort, { once: true })

    try {
        const stream = await createStream(abortSignal)

        onAbort = () => {
            stream.destroy()
        }

        const handlers: Record<string, Fn> = {
            metadata: () => promise.resolve(stream),
            end: () => promise.reject(new StreamError('Stream ended before subscription completed')),
            error: (error: unknown) => promise.reject(error),
            status: (status) => promise.reject(new StreamError('Stream status received before subscription completed').withValue('status', status)),
            close: () => promise.reject(new StreamError('Stream closed before subscription completed')),
        }

        for (const [event, handler] of Object.entries(eventHandlers)) {
            handlers[event] = (...args: any[]) => handler(promise, ...args)
        }

        cleanup = () => {
            for (const [event, handler] of Object.entries(handlers)) {
                stream.removeListener(event, handler)
            }
        }

        for (const [event, handler] of Object.entries(handlers)) {
            stream.once(event, handler)
        }
    } catch (error) {
        promise.reject(error)
    }

    return promise.finally(() => {
        cleanup?.()
        abortSignal.removeEventListener('abort', handleAbort)
    })
}
