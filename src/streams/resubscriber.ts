import type { Emitter } from '@kdt310722/utils/event'
import { transform } from '@kdt310722/utils/function'
import { type Awaitable, abortable, sleep } from '@kdt310722/utils/promise'

export enum ResubscribeReason {
    ABORTED = 'ABORTED',
    NOT_ENABLED = 'NOT_ENABLED',
    CIRCUIT_BREAKER_TRIPPED = 'CIRCUIT_BREAKER_TRIPPED',
    ERROR = 'ERROR',
    MAX_RETRIES_EXCEEDED = 'MAX_RETRIES_EXCEEDED',
    EXPLICITLY_CLOSED = 'EXPLICITLY_CLOSED',
}

export interface ResubscriberOptions {
    enabled?: boolean
    retries?: number
    delay?: number
    backoff?: number
    jitter?: number
    maxDelay?: number
    autoReset?: boolean
    circuitBreakerTimeout?: number
    shouldResubscribe?: (error?: unknown) => boolean
}

export type ResubscriberEvents = {
    error: (error: unknown) => void
    waitForResubscribe: (delay: number) => void
    resubscribe: (attempt: number, retriesLeft: number) => void
    resubscribed: () => void
    circuitBreakerTripped: (lastResubscribeSuccessTime: number) => void
    resubscribeAbandoned: (reason: ResubscribeReason) => void
}

export const DEFAULT_RESUBSCRIBER_OPTIONS: Required<ResubscriberOptions> = {
    enabled: true,
    retries: 5,
    delay: 1000,
    backoff: 2,
    jitter: 0,
    maxDelay: 10_000,
    autoReset: true,
    circuitBreakerTimeout: 60_000,
    shouldResubscribe: () => true,
}

export class Resubscriber {
    protected readonly options: Required<ResubscriberOptions>

    protected isResubscribing = false
    protected attempts = 0
    protected lastSuccessTime?: number
    protected afterResubscribed?: () => Awaitable<void>

    public constructor(protected readonly emitter: Emitter<ResubscriberEvents>, protected readonly subscriber: (signal?: AbortSignal) => Promise<void>, options: ResubscriberOptions = {}) {
        this.options = { ...DEFAULT_RESUBSCRIBER_OPTIONS, ...options }
    }

    public isCircuitBreakerTripped(lastSuccessTime: number) {
        return Date.now() - lastSuccessTime < this.options.circuitBreakerTimeout
    }

    public onResubscribed(afterResubscribed: () => Awaitable<void>) {
        this.afterResubscribed = afterResubscribed
    }

    public reset() {
        this.attempts = 0
        this.lastSuccessTime = undefined
    }

    public resubscribe(error?: unknown, signal?: AbortSignal) {
        if (signal?.aborted) {
            this.emitter.emit('resubscribeAbandoned', ResubscribeReason.ABORTED)

            return
        }

        if (this.isResubscribing) {
            return
        }

        if (!this.options.enabled || !this.options.shouldResubscribe(error)) {
            this.emitter.emit('resubscribeAbandoned', ResubscribeReason.NOT_ENABLED)

            return
        }

        if (this.lastSuccessTime && this.isCircuitBreakerTripped(this.lastSuccessTime)) {
            this.emitter.emit('circuitBreakerTripped', this.lastSuccessTime)
            this.emitter.emit('resubscribeAbandoned', ResubscribeReason.CIRCUIT_BREAKER_TRIPPED)

            return
        }

        this.isResubscribing = true

        const handleError = (error: unknown) => {
            this.emitter.emit('error', error)
            this.emitter.emit('resubscribeAbandoned', ResubscribeReason.ERROR)
        }

        const promise = this.process(signal).then(() => true).catch(handleError).finally(() => {
            this.isResubscribing = false
        })

        promise.then(async (r) => r === true && await this.afterResubscribed?.())
    }

    protected async process(signal?: AbortSignal) {
        if (this.attempts >= this.options.retries) {
            this.emitter.emit('resubscribeAbandoned', ResubscribeReason.MAX_RETRIES_EXCEEDED)

            return
        }

        const delay = this.getResubscribeDelay(++this.attempts)

        if (delay > 0) {
            this.emitter.emit('waitForResubscribe', delay)
        }

        const isWaited = await abortable(sleep(delay), signal).then(() => this.emitter.emit('resubscribe', this.attempts, this.options.retries - this.attempts)).catch(() => false)

        if (!isWaited) {
            this.emitter.emit('resubscribeAbandoned', ResubscribeReason.ABORTED)

            return
        }

        await this.subscriber(signal)

        this.lastSuccessTime = Date.now()

        if (this.options.autoReset) {
            this.attempts = 0
        }

        this.emitter.emit('resubscribed')
    }

    protected getResubscribeDelay(attempts: number) {
        const baseDelay = Math.min(this.options.delay * this.options.backoff ** (attempts - 1), this.options.maxDelay)

        if (this.options.jitter > 0) {
            return transform(baseDelay * this.options.jitter, (jitter) => baseDelay - (jitter / 2) + (Math.random() * jitter))
        }

        return baseDelay
    }
}
