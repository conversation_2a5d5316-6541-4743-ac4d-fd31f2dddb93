import { Emitter } from '@kdt310722/utils/event'

export type IdleTimerEvents = {
    error: (error: unknown) => void
}

export class IdleTimer extends Emitter<IdleTimerEvents> {
    protected timeoutId?: NodeJS.Timeout
    protected startTime?: number
    protected remainingTime?: number
    protected lastResetTime?: number
    protected isRunning = false
    protected isStopped = false

    public constructor(protected readonly timeout: number, protected readonly onTimeout: () => void, protected readonly debounce = 10) {
        super()
    }

    public start() {
        this.stop()
        this.isStopped = false
        this.startTime = Date.now()
        this.timeoutId = this.createTimeout(this.timeout)
    }

    public stop() {
        this.isStopped = true

        if (this.timeoutId) {
            clearTimeout(this.timeoutId)
            this.timeoutId = undefined
        }
    }

    public reset() {
        const now = Date.now()

        if (this.lastResetTime && now - this.lastResetTime < this.debounce) {
            return
        }

        this.lastResetTime = now
        this.stop()
        this.start()
    }

    public pause() {
        if (this.timeoutId && this.startTime) {
            this.remainingTime = this.timeout - (Date.now() - this.startTime)
            this.stop()
        }
    }

    public resume() {
        if (this.remainingTime) {
            this.timeoutId = setTimeout(() => this.handleTimeout(), this.remainingTime)
        }
    }

    protected createTimeout(timeout: number) {
        return setTimeout(() => this.handleTimeout(), timeout)
    }

    protected handleTimeout() {
        if (this.isStopped || this.isRunning) {
            return
        }

        this.isRunning = true

        try {
            this.onTimeout()
        } catch (error) {
            this.emit('error', error)
        } finally {
            this.isRunning = false
        }
    }
}
