import type { CallOptions, ClientDuplexStream, Metadata } from '@grpc/grpc-js'
import { notNullish } from '@kdt310722/utils/common'
import { DuplexStreamWrapper, type DuplexStreamWrapperOptions } from '../../../streams/duplex-stream-wrapper'
import { EMPTY_METADATA } from '../../../utils'
import type { GeyserClient, SubscribeRequest, SubscribeUpdate } from '../generated/geyser'
import { resolveHeartbeatOptions } from './options'
import { createPingMessage } from './requests'

export type YellowstoneGeyserSubscribeStream = ClientDuplexStream<SubscribeRequest, SubscribeUpdate>

export interface YellowstoneGeyserStreamWrapperOptions extends DuplexStreamWrapperOptions<SubscribeRequest> {
    metadata?: Metadata
    callOptions?: CallOptions
    updateOnResubscribed?: boolean
}

export class YellowstoneGeyserStreamWrapper extends DuplexStreamWrapper<SubscribeRequest, SubscribeUpdate> {
    protected lastRequest?: SubscribeRequest

    public constructor(protected readonly client: GeyserClient, { metadata = EMPTY_METADATA, callOptions, heartbeat = true, updateOnResubscribed = true, ...options }: YellowstoneGeyserStreamWrapperOptions = {}) {
        super(() => this.client.subscribe(metadata, callOptions), { heartbeat: resolveHeartbeatOptions(heartbeat), ...options })
    }

    public async update() {
        if (notNullish(this.lastRequest)) {
            return this.write(this.lastRequest)
        }
    }

    public ping(id?: number) {
        return this.write(createPingMessage(id))
    }
}
