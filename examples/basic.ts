import { stringifyError } from '@kdt310722/utils/error'
import { tap } from '@kdt310722/utils/function'
import { formatNanoseconds, isBigInt } from '@kdt310722/utils/number'
import { YellowstoneGeyserClient, createSubscribeRequest } from '../src/clients/yellowstone'

const getErrorMessage = (error: unknown) => {
    if (error instanceof Error) {
        return stringifyError(error)
    }

    return error
}

const stringify = (data: unknown) => {
    return JSON.stringify(data, (_, value) => {
        if (isBigInt(value)) {
            return value.toString()
        }

        return value
    })
}

async function run() {
    // const client = new YellowstoneGeyserClient('http://2.57.215.164:10101')

    const client = new YellowstoneGeyserClient('https://solana-yellowstone-grpc.publicnode.com')

    console.log('Version:', await client.getVersion({}))
    console.log('Blockhash:', await client.getLatestBlockhash({}))

    const stream = client.createStream({
        heartbeat: {
            interval: 1000,
            timeout: 100,
        },
        timeout: {
            subscribe: 20_000,
        },
        resubscribe: {
            delay: 1000,
            shouldResubscribe: (error) => tap(true, () => console.log('Resubscribe check:', getErrorMessage(error))),
        },
    })

    process.on('SIGINT', () => {
        console.log('SIGINT received, closing stream...')

        stream.close().catch((error) => {
            console.error('Error closing stream:', getErrorMessage(error))
            process.exit(1)
        })

        client.grpc.close()
    })

    stream.on('state', (state) => console.log('State:', state))

    // stream.on('data', (data) => console.log('Data received:', stringify(data)))
    stream.on('error', (error) => console.error(getErrorMessage(error)))
    stream.on('subscribed', () => console.log('Stream subscribed'))
    stream.on('closed', () => console.log('Stream closed'))
    stream.on('pause', () => console.log('Stream paused'))
    stream.on('resume', () => console.log('Stream resumed'))
    stream.on('status', (status) => console.log('Stream status:', status))
    stream.on('waitForResubscribe', (delay) => console.log(`Will resubscribe in ${formatNanoseconds(BigInt(delay * 1e6))}`))
    stream.on('resubscribe', (attempt, retriesLeft) => console.log('Stream resubscribe:', 'attempt', attempt, 'retriesLeft', retriesLeft))
    stream.on('circuitBreakerTripped', (lastResubscribeSuccessTime) => console.log('Circuit breaker tripped', new Date(lastResubscribeSuccessTime).toISOString()))
    stream.on('resubscribed', () => console.log('Stream resubscribed'))

    // stream.on('write', (data) => console.log('Stream write:', stringify(data)))
    stream.on('wrote', (data) => console.log('Stream wrote:', stringify(data)))

    stream.on('resubscribeAbandoned', (reason) => {
        console.log('Resubscribe Abandoned:', reason)
        process.exit(1)
    })

    stream.on('closeError', (error) => {
        console.log('closeError', getErrorMessage(error))
        process.exit(1)
    })

    await stream.subscribe()

    stream.write(createSubscribeRequest({ blocksMeta: { block: {} } }))
}

run().catch((error) => {
    console.error(getErrorMessage(error))
    process.exit(1)
})
